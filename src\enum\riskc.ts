import { enumToArray } from '@/script';

/**
 * 触发风控阈值比较符
 */
export enum RiskTriggerComparer {
  '>' = 1,
  '<' = 2,
}

/**
 * 触发风控阈值比较符
 */
export const RISK_TRIGGER_COMPARERS = enumToArray(RiskTriggerComparer);

/**
 * 触发风控采取动作
 */
export const RiskTriggerAction = {
  Warning: { label: '预警', value: 1 },
  Prevention: { label: '阻止', value: 2 },
  // ForbiddenOfCancel: { label: '禁止撤单', value: 3 },
  // ForbiddenOfOrder: { label: '禁止下单', value: 4 },
};

/**
 * 触发风控采取动作
 */
export const RiskTriggerActions = Object.values(RiskTriggerAction);

/**
 * 风控环节控制
 */
export const RiskStepControl = {
  instruction: { label: '指令', value: 1 },
  order: { label: '委托', value: 2 },
  instruction_and_order: { label: '指令+委托', value: 3 },
};

/**
 * 风控环节控制
 */
export const RiskStepControls = Object.values(RiskStepControl);

/**
 * 风控检查时机
 */
export const RiskCheckTime = {
  before: { label: '事前', value: 1 },
  progressing: { label: '事中', value: 2 },
  after: { label: '事后', value: 3 },
};

/**
 * 风控检查时机
 */
export const RiskCheckTimes = Object.values(RiskCheckTime);
