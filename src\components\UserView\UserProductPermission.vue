<script setup lang="tsx">
import { ref, computed, shallowRef, watch, onBeforeMount } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Repos,
  type MomUser,
  type LegacyFundInfo,
  type LegacyAccountInfo,
} from '../../../../xtrade-sdk/dist';
import VirtualizedTable from '../common/VirtualizedTable.vue';
import type { ColumnDefinition, RowAction } from '@/types';
import { formatDateTime, thousands } from '@/script';

interface CellRenderParam {
  rowData: LegacyFundInfo;
  cellData: any;
}

const { user } = defineProps<{
  user?: MomUser;
}>();

// 仓库实例
const adminRepo = new Repos.AdminRepo();
const governanceRepo = new Repos.GovernanceRepo();

// 响应式数据
const userProducts = shallowRef<LegacyFundInfo[]>([]);
const allProducts = shallowRef<LegacyFundInfo[]>([]);
const selectedProductForShare = ref<string>('');

// 计算属性：可选择的产品（排除已绑定的）
const availableProducts = computed(() => {
  const userProductIds = userProducts.value.map(p => p.id);
  return allProducts.value.filter(p => !userProductIds.some(x => x == p.id));
});

function renderNameCol(params: CellRenderParam) {
  const { fundName, strategyType } = params.rowData;
  return (
    <div class="fund-name-cell">
      <div class="fund-name toe">{fundName}</div>
      <div class="fund-strategy toe c-[--g-text-color-4]">{strategyType}</div>
    </div>
  );
}

function renderRealtimeNavCol(params: CellRenderParam) {
  const { navRealTime, latestNavDate } = params.rowData;
  const date = latestNavDate ? formatDateTime(latestNavDate, 'yy.MM.dd') : '--';
  return (
    <span>
      <span class="realtime-nav">
        {typeof navRealTime == 'number' ? navRealTime.toFixed(4) : '--'}
      </span>
      <br />
      <span class="realtime-nav-date">{date}</span>
    </span>
  );
}

function renderNavCol(params: CellRenderParam) {
  const { nav } = params.rowData;
  return <span>{typeof nav == 'number' ? nav.toFixed(4) : '--'}</span>;
}

function renderShareCol(params: CellRenderParam) {
  const { fundAccounts } = params.rowData;
  const share = fundAccounts.reduce((acc, cur) => acc + cur.fundShare, 0);
  return <span>{thousands(share)}</span>;
}

function render2Thousands(params: CellRenderParam) {
  return <span>{thousands(params.cellData)}</span>;
}

function renderWithNull() {
  return <span>--</span>;
}

function renderHeader(params: any) {
  const title = params.column.title! as string;
  return (
    <span>
      <span>{title.substring(0, 2)}</span>
      <br />
      <span>{title.substring(2)}</span>
    </span>
  );
}

// 用户产品表格列定义
const userProductColumns: ColumnDefinition<LegacyFundInfo> = [
  {
    key: 'fundName',
    title: '基金名字',
    width: 180,
    sortable: true,
    fixed: true,
    cellRenderer: renderNameCol,
  },
  {
    key: 'navRealTime',
    title: '最新净值',
    width: 90,
    sortable: true,
    fixed: true,
    align: 'right',
    cellRenderer: renderRealtimeNavCol,
  },
  {
    key: 'nav',
    title: '累计净值',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: renderNavCol,
  },
  {
    key: 'fundAccounts',
    title: '产品份额',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: renderShareCol,
  },
  {
    key: 'balance',
    title: '总资产',
    width: 150,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'id',
    title: '收益率',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: renderWithNull,
  },
  {
    key: 'id',
    title: '总负债',
    width: 90,
    sortable: true,
    align: 'right',
    cellRenderer: renderWithNull,
  },
  {
    key: 'marketValue',
    title: '证券总资产',
    width: 90,
    sortable: true,
    align: 'right',
    headerCellRenderer: renderHeader,
    cellRenderer: render2Thousands,
  },
  {
    key: 'marketValue',
    title: '证券总市值',
    width: 90,
    sortable: true,
    align: 'right',
    headerCellRenderer: renderHeader,
    cellRenderer: render2Thousands,
  },
  {
    key: 'marketValue',
    title: '股票总市值',
    width: 90,
    sortable: true,
    align: 'right',
    headerCellRenderer: renderHeader,
    cellRenderer: render2Thousands,
  },
];

// 产品账号表格列定义
const accountColumns: ColumnDefinition<LegacyAccountInfo> = [
  { key: 'accountName', title: '账号名称', width: 200, sortable: true },
  { key: 'accountId', title: '账号ID', width: 150, sortable: true },
  {
    key: 'assetType',
    title: '资产类型',
    width: 120,
    sortable: true,
    cellRenderer: ({ cellData }) => (
      <span>{cellData === 1 ? '现金' : cellData === 2 ? '信用' : '其他'}</span>
    ),
  },
  {
    key: 'balance',
    title: '总权益',
    width: 120,
    sortable: true,
    cellRenderer: ({ cellData }) => <span>{(cellData || 0).toFixed(2)}</span>,
  },
  {
    key: 'available',
    title: '可用资金',
    width: 120,
    sortable: true,
    cellRenderer: ({ cellData }) => <span>{(cellData || 0).toFixed(2)}</span>,
  },
];

// 用户产品行操作
const userProductRowActions: RowAction<LegacyFundInfo>[] = [
  {
    label: '取消分享',
    type: 'danger',
    onClick: (row: LegacyFundInfo) => {
      handleUnshareProduct(row);
    },
  },
];

// 加载分享给用户的产品列表
const loadUserProducts = async () => {
  if (!user?.id) return;

  const { errorCode, errorMsg, data } = await adminRepo.QueryUserProducts(user.id);
  if (errorCode === 0) {
    userProducts.value = (data || []).map(fund => {
      const matched = allProducts.value.find(item => item.id == fund.id);
      if (matched) {
        return matched;
      } else {
        return fund;
      }
    });
  } else {
    ElMessage.error(errorMsg || '加载用户产品失败');
  }
};

// 加载所有产品列表
const loadAllProducts = async () => {
  const { errorCode, errorMsg, data } = await governanceRepo.QueryProducts();
  if (errorCode === 0) {
    allProducts.value = data || [];
  } else {
    ElMessage.error(errorMsg || '加载产品列表失败');
  }
};

// 分享产品给用户
const handleShareProduct = async (id: string) => {
  const fund = allProducts.value.find(item => item.id == id)!;
  const { errorCode, errorMsg } = await adminRepo.ShareUserProducts(user!.id, [
    {
      id: Number(fund.id),
      fundType: fund.fundType,
      fundName: fund.fundName,
    },
  ]);

  if (errorCode === 0) {
    ElMessage.success('分享成功');
    selectedProductForShare.value = '';
    await loadUserProducts();
  } else {
    ElMessage.error(errorMsg || '分享失败');
  }
};

// 取消分享产品
const handleUnshareProduct = async (product: LegacyFundInfo) => {
  if (!user?.id) return;

  const { errorCode, errorMsg } = await adminRepo.UnshareUserProducts(user.id, product.id);

  if (errorCode === 0) {
    ElMessage.success('取消分享成功');
    await loadUserProducts();
  } else {
    ElMessage.error(errorMsg || '取消分享失败');
  }
};

// 监听用户变化
watch(
  () => user?.id,
  async newUserId => {
    if (newUserId) {
      await loadAllProducts();
      loadUserProducts();
    }
  },
  { immediate: true },
);
</script>

<template>
  <div class="user-product-permission" p-4>
    <!-- 产品选择和分享 -->
    <div w-200 mt-10>
      <el-select
        v-model="selectedProductForShare"
        placeholder="选择要分享的产品"
        filterable
        clearable
        @change="handleShareProduct"
      >
        <el-option
          v-for="product in availableProducts"
          :key="product.id"
          :label="product.fundName"
          :value="product.id"
        />
      </el-select>
    </div>

    <!-- 分享给用户的产品列表 -->
    <div class="user-products-section" mb-6>
      <h3 class="section-title" mb-3>分享给用户的产品</h3>
      <div h-300>
        <VirtualizedTable
          :columns="userProductColumns"
          :data="userProducts"
          :row-actions="userProductRowActions"
          :row-action-width="120"
          :show-toolbar="false"
          fixed
        />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
