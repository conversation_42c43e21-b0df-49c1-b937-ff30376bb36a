/**
 * to format date time(can be Date object, UTC timestamp, date time string) with a given pattern
 * @param source input value to be formatted
 * @param pattern output pattern string(by default = 'yyyy-MM-dd hh:mm:ss')
 */
export const formatDateTime = (source: Date | number | string, pattern?: string): string => {
  if (!source || source == '--') {
    return '--';
  }

  /**
   * [yyyyMMdd] formatter is commonly used across the web
   */
  if (typeof source == 'number' && source.toString().length == 8) {
    source = source.toString();
  }

  if (typeof source == 'string' && /^\d{8}$/.test(source)) {
    source = `${source.substring(0, 4)}/${source.substring(4, 6)}/${source.substring(6, 8)}`;
  }

  /**
   * the minimal length is [yyyyMMdd]/8
   */
  if (typeof source == 'string' && source.length < 8) {
    return source;
  }

  if (pattern == undefined || pattern == null) {
    pattern = 'yyyy-MM-dd hh:mm:ss';
  }

  const dt = source instanceof Date ? source : new Date(source);
  const o: any = {
    'M+': dt.getMonth() + 1,
    'd+': dt.getDate(),
    'h+': dt.getHours(),
    'm+': dt.getMinutes(),
    's+': dt.getSeconds(),
    'q+': Math.floor((dt.getMonth() + 3) / 3),
    S: dt.getMilliseconds(),
  };

  if (/(y+)/.test(pattern)) {
    pattern = pattern.replace(RegExp.$1, (dt.getFullYear() + '').substr(4 - RegExp.$1.length));
  }

  for (const k in o) {
    if (new RegExp('(' + k + ')').test(pattern)) {
      pattern = pattern.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length),
      );
    }
  }

  return pattern;
};

/**
 * 渲染枚举类型文本
 * @param value 值
 * @param enums 枚举值列表
 */
export const renderLabel = (
  value: unknown,
  enums:
    | { label: string; value: unknown }[]
    | { Label: string; Value: unknown }[]
    | [key: string, value: unknown][],
): string | unknown => {
  if (!Array.isArray(enums)) {
    return value;
  }
  const ismatrix = enums[0] instanceof Array;
  if (ismatrix) {
    const matched = (enums as [string, unknown][]).find(x => x[1] === value);
    return matched ? matched[0] : value || '--';
  } else {
    const islower = 'label' in enums[0];
    if (islower) {
      return (
        (enums as { label: string; value: unknown }[]).find(x => x.value === value)?.label ||
        (value === null || value === undefined ? '--' : value)
      );
    } else {
      return (
        (enums as { Label: string; Value: unknown }[]).find(x => x.Value === value)?.Label ||
        (value === null || value === undefined ? '--' : value)
      );
    }
  }
};

/**
 * 对数值进行千分位操作
 * @param number 要进行千分位格式化的目标数值
 * @param precision 小数精度位数，默认为2位
 */
export const thousands = (number: number, precision = 2) => {
  if (typeof number != 'number') {
    return number || '--';
  }

  const num_str = precision == 0 ? Math.round(number).toString() : number.toFixed(precision);
  const parts = num_str.split('.');
  const exp = /\d{1,3}(?=(\d{3})+(\.\d*)?$)/g;
  return parts[0].replace(exp, '$&,') + (parts.length > 1 ? '.' + parts[1].replace(/,/g, '') : '');
};

/**
 * 格式化数值为千分位（整数）
 */
export const thousandsInt = (number: number): string | number => {
  return thousands(number, 0);
};
