/**
 * 报警阀值定义，并定义和净值的关系，是否还需要？直接作为riskrule的configuration的内容是否合适？
 */
export interface RiskWarning {
  /**
   * 主键ID
   */
  id: number;

  /**
   * 风控条件id
   */
  riskRuleId: number;

  /**
   * 预警类型
   */
  warningType: number;

  /**
   * 最小预警值
   */
  minWarningValue?: number | null;

  /**
   * 最大预警值
   */
  maxWarningValue?: number | null;

  /**
   * 最小相对值
   */
  minRelative?: string | null;

  /**
   * 最大相对值
   */
  maxRelative?: string | null;

  /**
   * 最小净值
   */
  minNetValue?: number | null;

  /**
   * 最大净值
   */
  maxNetValue?: number | null;

  /**
   * 最小净值相对值
   */
  minNetRelative?: string | null;

  /**
   * 最大净值相对值
   */
  maxNetRelative?: string | null;
}

/**
 * 风控模板，控制有哪些模板
 */
export interface RiskTemplate {
  /**
   * 风控模板id，最好用一个生成策略，和系统其他id绝对不同，方便使用相同的分享表
   */
  id: number;

  /**
   * 模板名字
   */
  riskTemplateName: string;

  /**
   * 是否全局风控，每个机构只有一个全局风控模板
   */
  globalRiskTemplate: boolean;

  /**
   * 机构ID
   */
  orgId: number;

  /**
   * 创建者用户名
   */
  createUserName: string;

  /**
   * 创建者用户ID
   */
  createUserId: number;

  /**
   * 更新时间
   */
  updateTime: number;

  /**
   * 创建时间
   */
  createTime: number;
}

/**
 * 风控条件设置，包括参数设置和关联的模板或者产品/账号，是风控指标的参数具体化，是执行风控的单元，每个产品和账号，根据这个表，构建产品/账号需要允许的风控条件列表，构建全局联合风控需要需要执行的风控条件
 * 事中风控如何起阻断作用，不是直接阻止，是通过动态新增，黑白名单， 账号状态， 产品状态，来起到作用，因为事前默认会运行这些风控条件
 */
export interface RiskRule {
  /**
   * 风控规则id，最好统一生成，不要用数据库id，避免重复
   */
  id: number;

  /**
   * 风控模板id，可以为0，表示没有绑定模板，是一个独立的风控规则，直接和产品/账号关联的
   */
  templateId: number;

  /**
   * 风控规则名称
   */
  ruleName: string;

  /**
   * 风控指标ID
   */
  indicatorId: number;

  /**
   * 风控指标的参数配置（JSON字符串？？）
   */
  configuration: any;

  /**
   * 风控规则起作用的一天中的开始时间，到秒钟130934
   */
  beginTime: number;

  /**
   * 风控规则起作用的一天中的结束时间
   */
  endTime: number;

  /**
   * 风控规则起作用的开始日期
   */
  beginDay: number;

  /**
   * 风控规则起作用的结束日期
   */
  endDay: number;

  /**
   * 检查对象 1. 指令 2. 委托 3. 指令+委托 ？ 可以同时勾选，会拆分为多个执行，指令来的时候，委托事前，事中定时
   */
  checkObject: number;

  /**
   * 检查时机，1 事前 2 事中。 事中和定时一起使用，委托的时候可以勾选事前/事中， 指令默认事前
   */
  checkTime: number;

  /**
   * 检查间隔，秒钟，
   */
  checkInterval: number;

  /**
   * 是否启用
   */
  active: boolean;

  /**
   * 创建者用户ID
   */
  createUserId: number;

  /**
   * 机构ID
   */
  orgId: number;

  /**
   * 创建时间
   */
  createTime: number;

  /**
   * 更新时间
   */
  updateTime: number;
}

/**
 * 风控消息历史记录
 */
export interface RiskMessageHistory {
  /**
   * 消息id
   */
  id: number;

  /**
   * 风控规则id
   */
  riskRuleId: number;

  /**
   * 报警类型
   */
  warningType: number;

  /**
   * 报警对象，全局，产品，账号，虚拟账号，虚拟产品，联合风控对象
   */
  identity: number;

  /**
   * 报警对象类型
   */
  identityType: number;

  /**
   * 报警对象名称
   */
  identityName?: string | null;

  /**
   * 报警内容
   */
  content: string;

  /**
   * 创建时间
   */
  createTime: Date;

  /**
   * 交易日
   */
  tradingDay: number;

  /**
   * 自然日
   */
  openDay: number;
}

/**
 * 风控消息记录
 */
export interface RiskMessage {
  /**
   * 消息id
   */
  id: number;

  /**
   * 风控规则id
   */
  riskRuleId: number;

  /**
   * 报警类型
   */
  warningType: number;

  /**
   * 报警对象，全局，产品，账号，虚拟账号，虚拟产品，联合风控对象
   */
  identity: number;

  /**
   * 报警对象类型
   */
  identityType: number;

  /**
   * 报警对象名称
   */
  identityName?: string | null;

  /**
   * 报警内容
   */
  content: string;

  /**
   * 创建时间
   */
  createTime: Date;

  /**
   * 交易日
   */
  tradingDay: number;

  /**
   * 自然日
   */
  openDay: number;
}

/**
 * 风控指标描述表，描述了指标和树结构，指标树最多2级。可以使用系统通用的分类树也可以。避免重复。应该用那个，因为全局风控使用的指标树还不同
 */
export interface RiskIndicator {
  /**
   * 手工给的指标编码，通过指标编码来做唯一标识，手工添加来维护，通过大小来控制顺序，可以有规则的编码，每一级预留3位数字，比如100001001
   */
  id: number;

  /**
   * 指标的中文名字
   */
  indicatorName: string;

  /**
   * 一级分类编码，比如 100
   */
  firstLevelCode: string;

  /**
   * 一级分类名字
   */
  firstLevelName: string;

  /**
   * 指标的解释说明和注释
   */
  description?: string | null;

  /**
   * 二级分类编码，比如100001
   */
  secondLevelCode: string;

  /**
   * 二级分类名称
   */
  secondLevelName: string;
}

/**
 * 动态函数计算公式的变量定义
 */
export interface RiskFormulaVariable {
  /**
   * 数据库id
   */
  id: number;

  /**
   * 风控条件id，属于哪个风控条件
   */
  riskRuleId: number;

  /**
   * 风控计算指标id
   */
  riskComputeCunctionId: number;

  /**
   * 统计的分类
   */
  classificationId: number;

  /**
   * 在公式中的变量名字，比如A，B，C，D
   */
  rangeCode: string;

  /**
   * 汇总方式，按照合约，按照分类
   */
  byInstrument: number;

  /**
   * 在公式中的权重，不如1.0，0.8
   */
  weight: number;
}

/**
 * 计算函数表，维护系统支持哪些固定的计算函数，这些计算函数接受一些固定的参数，根据参数去理解和解析构建
 */
export interface RiskComputeFunction {
  /**
   * 计算函数编码，手工增加,相同分类下按照顺序排序
   */
  id: number;

  /**
   * 计算函数名字
   */
  computeFunctionName?: number | null;

  /**
   * 计算函数分类编码
   */
  categoryId?: number | null;

  /**
   * 计算函数分类名称
   */
  categoryName?: string | null;

  /**
   * 计算函数说明
   */
  description?: string | null;
}

/**
 * 资产类别
 */
export interface KindOfAsset {
  id: number;
  /** 底层节点编码 */
  kindCode: string;
  /** 底层节点名称 */
  kindName: string;
  /** 中层节点编码 */
  middleKindCode: string;
  /** 中层节点名称 */
  middleKindName: string;
  /** 顶层节点编码 */
  parentKindCode: string;
  /** 顶层节点名称 */
  parentKindName: string;
}

/**
 * 模板绑定到的产品信息
 */
export interface TemplateBoundOnProduct {

  id: number;
  fundId: number;
  fundName: string;
}

/**
 * 模板绑定到的账号信息
 */
export interface TemplateBoundOnAccount {

  id: number;
  accountId: number;
  accountName: string;
}