<script setup lang="ts">
import { ref, computed, onMounted, reactive, shallowRef } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { chooseConfirm, deleteConfirm, type ChooseConfirmDialogOption } from '@/script/interaction';
import { Repos, type RiskTemplate } from '../../../../xtrade-sdk/dist';
import { getUser, remove } from '@/script';
import type { AccountInfo, ProductInfo } from '@/types';

const repoInstance = new Repos.RiskControlRepo();
const repoGovenInstance = new Repos.GovernanceRepo();
const riskTemplates = ref<RiskTemplate[]>([]);
const searchKeyword = ref('');
const focusedId = ref<number | null>(null);

const dialogEdit = reactive({
  visible: false,
  name: '',
  current: null as RiskTemplate | null,
});

const title = computed(() => {
  const { current } = dialogEdit;
  return current ? '风控模板重命名' : '添加风控模板';
});

const filteredTemplates = computed(() => {
  const kw = (searchKeyword.value || '').toLowerCase();
  return riskTemplates.value.filter(template =>
    template.riskTemplateName.toLowerCase().includes(kw),
  );
});

const emitter = defineEmits<{
  select: [item: RiskTemplate | null];
  toggle: [];
}>();

const handleSelect = (target: RiskTemplate | null) => {
  focusedId.value = target ? target.id : null;
  emitter('select', target);
};

const handleEdit = (tmpl?: RiskTemplate) => {
  const obj = dialogEdit;
  obj.name = tmpl?.riskTemplateName || '';
  obj.visible = true;
  obj.current = tmpl || null;
};

async function QueryBoundDetails(template_id: number) {
  const resp1 = await repoInstance.QueryTemplateBoundProducts(template_id);
  const resp2 = await repoInstance.QueryTemplateBoundAccounts(template_id);
  const products = resp1.data || [];
  const accounts = resp2.data || [];

  products.forEach(x => {
    x.id = x.id.toString() as any;
  });
  accounts.forEach(x => {
    x.id = x.id.toString() as any;
  });
  return { products, accounts };
}

async function handleDelete(current: RiskTemplate) {
  const { products, accounts } = await QueryBoundDetails(current.id);
  const message = `确认删除此模板： ${current.riskTemplateName}<br/><br/>目前此模版已绑定${products.length}个产品和${accounts.length}个账号，删除后产品和账号的风控设置将被清除？`;
  const result = await deleteConfirm('删除模板', message);

  if (result !== true) {
    return;
  }

  const resp = await repoInstance.DeleteTemplate(current.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('已删除');
    const tmpls = riskTemplates.value;
    remove(tmpls, x => x.id == current.id);
    const target = tmpls.length > 0 ? tmpls[0] : null;
    handleSelect(target);
  } else {
    ElMessage.error(`删除失败：${errorCode}/${errorMsg}`);
  }
}

const handleToggle = () => {
  emitter('toggle');
};

const handleCancel = () => {
  closeDialog();
};

const closeDialog = () => {
  const obj = dialogEdit;
  obj.name = '';
  obj.visible = false;
  obj.current = null;
};

const handleConfirm = async () => {
  const { name, current } = dialogEdit;
  if (!name) {
    ElMessage.error('请输入模板名称');
    return;
  }

  const is_creation = !current;
  const is_edit = !is_creation;
  const matched_same_name = riskTemplates.value.find(item => item.riskTemplateName === name);

  if (matched_same_name) {
    if (is_creation) {
      ElMessage.error('该名称已存在');
      return;
    } else if (is_edit && current.riskTemplateName == name) {
      ElMessage.info('名称未变动');
      closeDialog();
      return;
    }
  }

  const user = getUser()!;
  const tmpl: RiskTemplate = {
    id: null as any,
    riskTemplateName: name,
    globalRiskTemplate: false,
    orgId: user.orgId,
    createTime: Date.now(),
    createUserId: user.userId,
    createUserName: user.username,
    updateTime: Date.now(),
  };

  if (is_edit) {
    Object.assign(tmpl, current);
    tmpl.riskTemplateName = name;
  }

  const resp = is_creation
    ? await repoInstance.CreateTemplate(tmpl)
    : await repoInstance.UpdateTemplate(tmpl);

  const { errorCode, errorMsg } = resp;
  const behavior = is_edit ? '重命名' : '创建';

  if (errorCode == 0) {
    ElMessage.success(`已${behavior}`);
    closeDialog();
    request();
  } else {
    ElMessage.error(`${behavior}失败：${errorCode}/${errorMsg}`);
  }
};

const dialogBinding = reactive({
  visible: false,
  type: 'product' as 'product' | 'account',
  current: null as RiskTemplate | null,
  targets: [] as string[],
});

const isBinding2Product = computed(() => dialogBinding.type == 'product');
const isBinding2Account = computed(() => dialogBinding.type == 'account');
const funds = shallowRef<ProductInfo[]>([]);
const accounts = shallowRef<AccountInfo[]>([]);

const handleBindProducts = async (tmpl: RiskTemplate) => {
  ShowBindingDialog(tmpl, 'product');
  const result = await QueryBoundDetails(tmpl.id);
  dialogBinding.targets = result.products.map(x => x.id.toString());
  requstFunds();
};

const handleBindAccounts = async (tmpl: RiskTemplate) => {
  ShowBindingDialog(tmpl, 'account');
  const result = await QueryBoundDetails(tmpl.id);
  dialogBinding.targets = result.accounts.map(x => x.id.toString());
  requstAccounts();
};

const ShowBindingDialog = (tmpl: RiskTemplate, type: any) => {
  const dialog = dialogBinding;
  dialog.visible = true;
  dialog.type = type;
  dialog.targets = [];
  dialog.current = tmpl;
};

const hideBindingDialog = () => {
  const dialog = dialogBinding;
  dialog.visible = false;
  dialog.type = 'product';
  dialog.current = null;
  dialog.targets = [];
};

const handleConfirmBinding = async () => {
  const { current, targets } = dialogBinding;
  const resp = isBinding2Product.value
    ? await repoInstance.BindTemplate2Products(current!.id, targets)
    : await repoInstance.BindTemplate2Accounts(current!.id, targets);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success(`目标已绑定，数量 = ${targets.length}`);
    hideBindingDialog();
  } else {
    ElMessage.error(`绑定失败：${errorCode}/${errorMsg}`);
  }
};

const dialogClone = reactive({
  visible: false,
  name: '',
  current: null as RiskTemplate | null,
});

const requstFunds = async () => {
  funds.value = (await repoGovenInstance.QueryProducts()).data || [];
};

const requstAccounts = async () => {
  accounts.value = (await repoGovenInstance.QueryAccounts()).data || [];
};

const handleClone = (tmpl: RiskTemplate) => {
  const dialog = dialogClone;
  dialog.name = `${tmpl.riskTemplateName}(克隆)`;
  dialog.visible = true;
  dialog.current = tmpl;
};

const hideCloneDialog = () => {
  const dialog = dialogClone;
  dialog.visible = false;
  dialog.name = '';
  dialog.current = null;
};

const handleConfirmClone = async () => {
  const { name, current } = dialogClone;
  if (!name) {
    ElMessage.error('请输入克隆模板名称');
    return;
  }

  const matched_same_name = riskTemplates.value.find(item => item.riskTemplateName === name);
  if (matched_same_name) {
    ElMessage.error('该名称已存在');
    return;
  }

  hideCloneDialog();
  execTemplateClone(current!, name);
};

const execTemplateClone = async (tmpl: RiskTemplate, template_name: string) => {
  const options: ChooseConfirmDialogOption = {
    confirmButtonText: '是的，将绑定的产品也克隆',
    cancelButtonText: '仅克隆模版',
  };
  const title = '克隆模版';
  const { products, accounts } = await QueryBoundDetails(tmpl.id);
  const message = `来源模版为 “${tmpl.riskTemplateName}” <br/><br/>此模版所绑定的：产品个数为${products.length}，账号个数为${accounts.length}，请选择是否需要克隆对应的绑定？`;
  const result = await chooseConfirm(title, message, options);

  if (result == 'close') {
    return;
  }

  const isfull = result == 'cancel' ? false : true;
  const resp = await repoInstance.CloneTemplate(tmpl.id, template_name, isfull);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('已克隆');
    closeDialog();
    request();
  } else {
    ElMessage.error(`克隆失败：${errorCode}/${errorMsg}`);
  }
};

async function request() {
  const list = (await repoInstance.QueryTemplates()).data || [];
  riskTemplates.value = list;
  const is_focused = focusedId.value && list.find(x => x.id == focusedId.value);

  if (list.length > 0 && !is_focused) {
    handleSelect(list[0]);
  }
}

function getCurrent() {
  return riskTemplates.value.find(x => x.id == focusedId.value);
}

const rulesCountMap = ref<Record<number, number>>({});

function setRulesCount(statistics: { templateId: number; ruleIds: number[] }[]) {
  const countMap: Record<number, number> = {};
  statistics.forEach(stat => {
    countMap[stat.templateId] = stat.ruleIds.length;
  });
  rulesCountMap.value = countMap;
}

defineExpose({
  getCurrent,
  setRulesCount,
});

onMounted(() => {
  request();
});
</script>

<template>
  <div class="risk-template-list" h-full p-10 flex flex-col>
    <div class="search-box" flex aic gap-12>
      <!-- 搜索框 -->
      <el-input
        v-model.trim="searchKeyword"
        placeholder="搜索模板"
        :suffix-icon="Search"
        clearable
      />

      <!-- 添加按钮 -->
      <el-tooltip placement="top" content="添加模板">
        <i class="iconfont icon-add-new" thb1 @click="handleEdit()"></i>
      </el-tooltip>

      <!-- 隐藏显示按钮 -->
      <el-tooltip placement="top" content="隐藏|显示该列表">
        <i class="iconfont icon-exchange" thb1 @click="handleToggle"></i>
      </el-tooltip>
    </div>

    <!-- 风控模板列表 -->

    <div h-40 lh-45 fs-14 fw-400 pl-15>全部风控（{{ riskTemplates.length }}）</div>

    <div class="templite-list" flex-1 mt-5>
      <div
        v-for="(tmpl, idx) in filteredTemplates"
        :key="idx"
        class="templite-item"
        :class="{ 'is-active': focusedId == tmpl.id }"
        h-40
        p-x-14
        flex
        jcsb
        aic
        gap-5
      >
        <div @click="handleSelect(tmpl)" h-full lh-40 flex-1 fs-14 fw-400>
          {{ tmpl.riskTemplateName }}
          <template v-if="rulesCountMap[tmpl.id] !== undefined">
            <span>({{ rulesCountMap[tmpl.id] }})</span>
          </template>
        </div>
        <el-popover
          popper-class="tmpl-oper-popover"
          trigger="click"
          placement="bottom"
          :width="150"
        >
          <template #reference>
            <div class="oper-ellipsis">
              <i class="iconfont icon-ellipsis" />
            </div>
          </template>
          <div class="riks-tmpl-oper-box" p-4 rd-8>
            <div class="oper-item" @click="handleEdit(tmpl)">
              <i class="iconfont icon-edit" />
              <span>重命名</span>
            </div>
            <div class="oper-item" @click="handleClone(tmpl)">
              <i class="iconfont icon-copy" />
              <span>克隆模版</span>
            </div>
            <div class="oper-item" @click="handleBindProducts(tmpl)">
              <i class="iconfont icon-consist-add" />
              <span>绑定到产品</span>
            </div>
            <div class="oper-item" @click="handleBindAccounts(tmpl)">
              <i class="iconfont icon-consist-add" />
              <span>绑定到账号</span>
            </div>
            <div class="oper-item" @click="handleDelete(tmpl)">
              <i class="iconfont icon-remove" />
              <span>删除模版</span>
            </div>
          </div>
        </el-popover>
      </div>
    </div>

    <!-- 添加模板对话框 -->
    <el-dialog v-model="dialogEdit.visible" :title="title" width="300px" draggable>
      <el-input v-model.trim="dialogEdit.name" placeholder="请输入风控模板名称" clearable />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 克隆模板对话框 -->
    <el-dialog v-model="dialogClone.visible" title="风控模版克隆" width="300px" draggable>
      <el-input v-model.trim="dialogClone.name" placeholder="请输入风控模板名称" clearable />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="hideCloneDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirmClone">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 绑定产品与账号对话框 -->
    <el-dialog
      v-model="dialogBinding.visible"
      :title="isBinding2Product ? '绑定模版到产品' : '绑定模版到账号'"
      width="400px"
      draggable
    >
      <el-select
        v-model="dialogBinding.targets"
        placeholder="请选择要绑定的对象"
        multiple
        collapse-tags
        filterable
        clearable
      >
        <template v-if="isBinding2Product">
          <el-option
            v-for="(item, idx) in funds"
            :key="idx"
            :label="item.fundName"
            :value="item.fundId"
          />
        </template>
        <template v-else-if="isBinding2Account">
          <el-option
            v-for="(item, idx) in accounts"
            :key="idx"
            :label="item.accountName"
            :value="item.accountId"
          />
        </template>
        <template #prefix>
          <i class="iconfont icon-block"></i>
        </template>
      </el-select>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="hideBindingDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirmBinding">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.risk-template-list {
  .templite-list {
    height: 500%;
    overflow-y: auto;
    .templite-item {
      &:hover,
      &.is-active {
        background-color: var(--g-block-bg-6);
      }
    }
    .oper-ellipsis {
      padding: 3px;
      border-radius: 4px;
      opacity: 0.5;
      &:hover {
        background-color: var(--g-bg-hover-1);
        opacity: 1;
      }
    }
  }
}
</style>

<style>
.tmpl-oper-popover {
  padding: 4px !important;
  min-width: 100px !important;
}

.riks-tmpl-oper-box {
  background-color: var(--g-bg);
  .oper-item {
    height: 40px;
    padding-left: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    border-radius: 4px;
    &:hover {
      background-color: var(--g-block-bg-6);
    }
  }
}
</style>
