<div class="view-fund-root">

    <div class="toolbar">

        <el-button v-if="isSuperAdmin || isBrokerAdmin" type="primary" size="mini" @click="create">
            <i class="el-icon-plus"></i> 创建产品
        </el-button>

        <el-input v-model="searching.keywords" class="input-searching s-mgl-10" placeholder="输入关键词过滤" @change="filterRecords" clearable>
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>

        <span class="pagination-wrapper">

            <el-pagination :page-sizes="paging.pageSizes" :page-size.sync="paging.pageSize" :total="paging.total"
                :current-page.sync="paging.page" :layout="paging.layout" @size-change="handlePageSizeChange"
                @current-change="handlePageChange"></el-pagination>
        </span>

        <div class="s-pull-right">

            <el-button size="mini" @click="refresh">
                <i class="iconfont icon-refresh"></i> 刷新</el-button>

            <el-button size="mini" @click="exportSome">
                <i class="iconfont icon-export"></i> 导出</el-button>

            <!-- <el-button size="mini" @click="config">
                <i class="iconfont icon-setting"></i> 配置</el-button> -->
        </div>

    </div>

    <table>
        <tr>
            <th label="产品名称" 
                min-width="180" 
                prop="fundName" fixed sortable overflowt>
                <a class="s-underline s-cp" event.onclick="viewReportTemplate" title="查看产品报告">$fundName$</a>
            </th>

            <th label="ID" min-width="140" prop="id" overflowt></th>

            <th label="类型"
                min-width="80" 
                prop="fundType" 
                formatter="formatFundType" 
                export-formatter="formatFundTypeText" sortable></th>

            <th label="备案号" min-width="110" prop="amacCode" overflowt></th>
            <th label="成立日期" min-width="100" prop="establishedDay" sortable overflowt></th>
            <th label="管理机构" min-width="150" prop="orgName" sortable overflowt></th>
            <th label="策略类型" min-width="150" prop="strategyType" sortable overflowt></th>

            <th label="报告模板" 
                min-width="100" 
                prop="reportTemplates" 
                formatter="formatReportTemplate" 
                export-formatter="formatReportTemplateText"
                condition="manage-only"
                overflowt></th>
            
            <th label="基金经理" min-width="90" prop="fundManager" overflowt></th>
            <th label="创建人" min-width="90" prop="creatorUserFullName" overflowt></th>

            <th label="分享用户" 
                min-width="150" 
                prop="users" 
                formatter="formatShares" 
                export-formatter="formatSharesText"
                condition="manage-only"
                overflowt></th>

            <th label="已绑账号"
                min-width="200" 
                prop="accounts" 
                formatter="formatAccount" 
                export-formatter="formatAccountText"
                condition="manage-only"
                overflowt></th>

            <th label="估值方式" 
                min-width="100" 
                prop="valuation" 
                formatter="formatValuation" 
                export-formatter="formatValuationText" 
                condition="manage-only"
                overflowt></th>

            <th label="权益"
                min-width="100" 
                prop="balance" 
                align="right"
                condition="org-only" 
                sortable
                summarizable 
                overflowt 
                thousands></th>

            <th label="市值"
                min-width="100" 
                prop="marketValue" 
                align="right" 
                condition="org-only" 
                sortable 
                summarizable 
                overflowt 
                thousands></th>

            <th label="收益率"
                min-width="80"
                prop="risePercent" 
                align="right" 
                class-maker="makeBenefitClass" 
                condition="org-only" 
                sortable 
                percentage 
                overflowt></th>

            <th label="当前净值"
                min-width="80" 
                prop="navRealTime" 
                align="right"
                precision="4"
                condition="org-only"  
                sortable></th>

            <th label="参考净值" 
                min-width="80" 
                prop="nav" 
                align="right" 
                precision="4" 
                condition="org-only"
                sortable>
            </th>

            <th label="场外资产" 
                min-width="80" 
                prop="offsiteAsset" 
                align="right"
                sortable
                thousands>
            </th>

            <th label="场外负债" 
                min-width="80" 
                prop="offsiteDebt" 
                align="right"
                sortable
                thousands>
            </th>
            
            <!--

            <th label="设置清盘" 
                min-width="80" 
                prop="closedFlag" 
                align="center" 
                formatter="formatFundStatus"
                export-formatter="formatFundStatusText" 
                condition="manage-only"
                sortable></th>

            <th label="启用风控" 
                min-width="80" 
                prop="riskEnable" 
                align="center" 
                formatter="formatRiskControled"
                export-formatter="formatRiskControledText" 
                condition="manage-only"
                sortable></th>

            -->

            <th label="操作" 
                fixed-width="70" 
                align="center" 
                fixed="right"
                exportable="false"
                condition="manage-only">

                <span class="smart-table-action">
                    <a class="lock-button icon-button">...</a>
                    <ul>
                        <li><a class="icon-button el-icon-edit" event.onclick="edit"> 编辑</a></li>
                        <li><a class="icon-button el-icon-setting" event.onclick="noteOffsite"> 录入场外数据</a></li>
                        <!--
                        <li><a class="icon-button iconfont icon-tianjiafengkong" event.onclick="openRiskControlSetting"> 风控设置</a></li>
                        <li><a class="icon-button iconfont icon-tab_yunwei" event.onclick="openBehaviorControlSetting"> 交易行为设置</a></li>
                        <li><a class="icon-button el-icon-copy-document iconfont" event.onclick="showFundPermissionClone"> 权限克隆</a></li> 
                         -->
                        <li><a class="icon-button el-icon-delete s-color-red" event.onclick="delete"> 删除产品</a></li>
                    </ul>
                </span>

            </th>

        </tr>
    </table>

    <div class="dialog-editing">

        <template>

            <el-dialog width="630px" :visible="dialog.visible" :title="dialog.title" :close-on-click-modal="false"
                :close-on-press-escape="false" :show-close="false" v-drag>

                <el-form ref="editform" class="dialog-body-form" label-width="110px" :model="formd" :rules="rules"
                    :inline="true">

                    <el-form-item label="产品名称" prop="fundName">
                        <el-input v-model.trim="formd.fundName" placeholder="请输入产品名称"></el-input>
                    </el-form-item>

                    <el-form-item label="基金类型" prop="fundType">
                        <el-select v-model="formd.fundType">
                            <el-option v-for="(item, item_idx) in fundTypes" :key="item_idx" :value="item.code"
                                :label="item.mean"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="备案号" prop="amacCode">
                        <el-input v-model.trim="formd.amacCode"></el-input>
                    </el-form-item>

                    <el-form-item label="成立日期" prop="establishedDay">
                        <el-date-picker v-model="formd.establishedDay" value-format="yyyy-MM-dd"></el-date-picker>
                    </el-form-item>

                    <el-form-item label="基金经理" prop="fundManager">
                        <el-input v-model.trim="formd.fundManager"></el-input>
                    </el-form-item>

                    <el-form-item label="策略类型" prop="strategyType">
                        <el-input v-model.trim="formd.strategyType"></el-input>
                    </el-form-item>

                    <el-form-item label="参考基准" prop="basisReference">
                        <el-select v-model="formd.basisReference">
                            <el-option v-for="ref in basisReferences" :key="ref.code" :value="ref.code"
                                :label="ref.mean"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="管理机构" prop="orgId">
                        <el-select v-model="formd.orgId" :disabled="isOrgFixed" @change="handleOrgChange"
                            placeholder="请选择所属机构" clearable filterable>
                            <el-option v-for="(item, item_idx) in orgs" :key="item_idx" :label="item.orgName"
                                :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>

                </el-form>

                <div slot="footer">
                    <el-button @click="save" type="primary" size="small">确定</el-button>
                    <el-button @click="unsave" size="small">取消</el-button>
                </div>

            </el-dialog>

        </template>
    </div>

    <div class="dialog-offsite">

        <template>

            <el-dialog 
                width="400px" 
                :visible="dialog.visible" 
                :title="dialog.title" 
                :close-on-click-modal="false"
                :close-on-press-escape="false" 
                :show-close="false" 
                v-drag
                >
                <div class="s-pd-10">
                    <el-form label-width="80px" :model="dialog">
                        <el-form-item label="产品名称" prop="fundName">
                            <el-input v-model.trim="dialog.fundName" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="场外负债" prop="offsiteDebt">
                            <el-input-number v-model="dialog.offsiteDebt" :precision="2" :min="0" :controls="false" style="width: 295px;"></el-input-number>
                        </el-form-item>
                        <el-form-item label="场外资产" prop="offsiteAsset">
                            <el-input-number v-model="dialog.offsiteAsset" :precision="2" :min="0" :controls="false" style="width: 295px;"></el-input-number>
                        </el-form-item>
                    </el-form>
                </div>
                <div slot="footer">
                    <el-button @click="saveOffsite" type="primary" size="small">确定</el-button>
                    <el-button @click="unsaveOffsite" size="small">取消</el-button>
                </div>
            </el-dialog>

        </template>
    </div>

    <div class="dialog-permission-clone">

        <template>

            <el-dialog width="800px" title="权限克隆" :show-close="false" :visible="dialog.visible" :close-on-click-modal="false" :close-on-press-escape="false" v-drag>
                <div v-if="permission.fundPermissionList.length <= 0">
                    <h2 align="center">无产品信息</h2>
                </div>
                <div v-else>
                    <div style="width: 100%;height: 30px;line-height: 30px;font-size: 14px;">
                        <el-checkbox style="margin:  0 30px;" :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
                    </div>
                    <div>
                        <el-checkbox-group v-model="checkedPermissionFund" @change="handleCheckedPermissionFundChange">
                            <data-tables layout="table,pagination" table-label="权限克隆":default-sort="{prop: 'id', order: 'descending'}"
                            ref="table" class="s-searchable-table" v-bind:data="permission.fundPermissionList"
                            v-bind:pagination-props="{ show: false, layout: 'prev,pager,next,sizes,total' }">
                                <el-table-column style="font-size: 14px" prop="index" fixed="left" show-overflow-tooltip label="序号" type="index" width="50"
                                    align="center">
                                </el-table-column>
                                <el-table-column style="font-size: 15px" fixed="left" show-overflow-tooltip label="产品ID" prop="id" width="200"
                                    sortable="custom">
                                    <template slot-scope="scope">
                                        <span style="font-size: 12px">{{scope.row.id}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column style="font-size: 15px" fixed="left" show-overflow-tooltip label="产品名称" prop="fundName" min-width="120"
                                    sortable="custom">
                                    <template slot-scope="scope">
                                        <span style="font-size: 12px">{{scope.row.fundName}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column style="font-size: 15px" prop="users" show-overflow-tooltip label="用户权限克隆"
                                    min-width="140" sortable="custom">
                                    <template slot-scope='props'>
                                        <el-checkbox :key="props.row.id" :label="props.row.id">
                                            <span v-if="props.row.traders && props.row.traders.length > 0" 
                                                style="font-size: 12px">{{props.row.users.map(user => user.fullName).join('、')}}</span>
                                            <span v-else style="font-size: 12px">未绑定</span>
                                        </el-checkbox>
                                    </template>
                                </el-table-column>   
                            </data-tables>
                        </el-checkbox-group>
                    </div>
                </div>
                <span slot="footer">
                    <el-button @click="saveFundPermissionClone" type="primary" size="small">确定</el-button>
                    <el-button @click="closeFundPermissionCloneDialog" size="small">取消</el-button>
                </span>
        </el-dialog>

        </template>
    </div>

    <div class="dialog-user">

        <template>

            <el-dialog title="分享产品给用户"
                        width="700px"
                        :visible="dialog.visible" 
                        :close-on-click-modal="false" 
                        :show-close="false"
                        v-drag>

                <div class="dialog-body-inner">

                    <div class="single-row">
                        <label>目标产品：</label>
                        <span class="s-bold">{{ dialog.fundName }}</span>
                    </div>

                    <div class="single-row">

                        <label>分享用户：</label>
                        <br>
                        <el-transfer filter-placeholder="关键字搜索"
                                     v-model="dialog.selected"
                                     :titles="['可选用户', '已选用户']"
                                     :data="dialog.users"
                                     :props="{ key: 'userId', label: 'userName' }" filterable></el-transfer>
                    </div>
                </div>
                <div slot="footer">
                    <el-button type="primary" size="small" @click="saveUser">确定</el-button>
                    <el-button size="small" @click="unsaveUser">取消</el-button>
                </div>
            </el-dialog>
        </template>
    </div>

    <div class="dialog-account">

        <template>
            <el-dialog :visible="dialog.visible" :close-on-click-modal="false" title="绑定账号" :show-close="false"
                width="700px" v-drag>
                <div class="dialog-body-inner">
                    <div class="single-row">
                        <label>目标产品：</label>
                        <span class="s-bold">{{ dialog.fundName }}</span>
                    </div>
                    <div class="single-row">
                        <label>选择账号：</label>
                        <br>
                        <el-transfer filter-placeholder="关键字搜索"
                                     v-model="dialog.selected"
                                     :titles="['可选账号', '已选账号']"
                                     :data="dialog.accounts"
                                     :props="{ key: 'accountId', label: 'accountName' }" filterable></el-transfer>

                    </div>
                </div>
                <div slot="footer">
                    <el-button type="primary" size="small" @click="saveAccount">确定</el-button>
                    <el-button size="small" @click="unsaveAccount">取消</el-button>
                </div>
            </el-dialog>
        </template>

    </div>

    <div class="dialog-template">

        <template>
            <el-dialog :visible="dialog.visible" :close-on-click-modal="false" title="绑定报告模板" :show-close="false"
                width="420px" v-drag>
                <div class="dialog-body-inner">
                    <div class="single-row">
                        <label>目标产品</label>
                        <span class="s-bold">{{ dialog.fundName }}</span>
                    </div>
                    <div class="single-row">
                        <label>选择模板</label>
                        <el-select v-model="dialog.selected" placeholder="选择需要绑定的模板" :clearable="true"
                            :filterable="true" multiple collapse-tags>
                            <el-option v-for="(item, item_idx) in dialog.templates" :key="item_idx"
                                :label="item.templateName" :value="item.templateId"></el-option>
                        </el-select>
                    </div>
                </div>
                <div slot="footer">
                    <el-button type="primary" size="small" @click="saveTemplate">确定</el-button>
                    <el-button size="small" @click="unsaveTemplate">取消</el-button>
                </div>
            </el-dialog>
        </template>

    </div>

    <div class="dialog-valuation">

        <template>
            <el-dialog :visible="dialog.visible" :close-on-click-modal="false" title="配置估值方式" :show-close="false"
                width="420px" v-drag>
                <div class="dialog-body-inner">
                    <div class="single-row">
                        <label>目标产品</label>
                        <span class="s-bold">{{ dialog.fundName }}</span>
                    </div>
                    <div class="single-row">
                        <label>选择估值方式号</label>
                        <el-select v-model="dialog.selected" placeholder="选择估值方式" :filterable="true">
                            <el-option v-for="(item, item_idx) in dialog.methods" :key="item_idx" :label="item.mean"
                                :value="item.code"></el-option>
                        </el-select>
                    </div>
                </div>
                <div slot="footer">
                    <el-button type="primary" size="small" @click="saveValuation">确定</el-button>
                    <el-button size="small" @click="unsaveValuation">取消</el-button>
                </div>
            </el-dialog>
        </template>

    </div>

</div>