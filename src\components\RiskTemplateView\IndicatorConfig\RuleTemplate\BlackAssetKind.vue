<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { RiskTriggerAction, RiskTriggerActions } from '@/enum/riskc';
import { deepClone, isJson } from '@/script';

interface RuleInnerSetting {
  alertType: { name: string; value: number };
}

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>({
  alertType: {
    name: RiskTriggerAction.Warning.label,
    value: RiskTriggerAction.Warning.value,
  },
});
const { ruleSetting } = defineProps<{ ruleSetting: RuleInnerSetting }>();

const rules = {
  'alertType.value': [{ required: true, message: '请选择对采取行动', trigger: 'blur' }],
};

watch(
  () => ruleSetting,
  () => {
    if (isJson(ruleSetting)) {
      localRuleSetting.value = deepClone(ruleSetting);
    }
  },
  { immediate: true },
);

function validate() {
  return $form.value!.validate();
}

function getSetting() {
  const matched = RiskTriggerActions.find(x => x.value == localRuleSetting.value.alertType.value);
  localRuleSetting.value.alertType.name = matched ? matched.label : '';
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getSetting,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" :rules="rules" label-width="80px">
      <div class="custom-row">
        <el-form-item label="指标设置" prop="alertType.value">
          <div w-full flex aic gap-10>
            <div w-auto>
              <label class="placed-label">黑名单</label>
            </div>
            <div flex-1>
              <el-select v-model="localRuleSetting.alertType.value" style="width: 140px">
                <el-option
                  v-for="(item, idx) in RiskTriggerActions"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
      .el-form-item__error {
        margin-left: 50px;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
