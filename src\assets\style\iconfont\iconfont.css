@font-face {
  font-family: 'iconfont'; /* Project id 4963126 */
  src:
    url('iconfont.woff2?t=1755846700048') format('woff2'),
    url('iconfont.woff?t=1755846700048') format('woff'),
    url('iconfont.ttf?t=1755846700048') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-warning-filling:before {
  content: '\e68b';
}

.icon-warning:before {
  content: '\e682';
}

.icon-copy:before {
  content: '\e62a';
}

.icon-consist-add:before {
  content: '\e62b';
}

.icon-ellipsis:before {
  content: '\e728';
}

.icon-tag:before {
  content: '\e629';
}

.icon-telephone:before {
  content: '\e622';
}

.icon-envelop:before {
  content: '\e623';
}

.icon-stadium:before {
  content: '\e624';
}

.icon-puzzle:before {
  content: '\e625';
}

.icon-text:before {
  content: '\e626';
}

.icon-rows:before {
  content: '\e628';
}

.icon-court:before {
  content: '\e61e';
}

.icon-unlink:before {
  content: '\e61f';
}

.icon-calendar-2:before {
  content: '\e620';
}

.icon-radio:before {
  content: '\e621';
}

.icon-code:before {
  content: '\e61d';
}

.icon-document-code:before {
  content: '\e735';
}

.icon-close:before {
  content: '\e61c';
}

.icon-save:before {
  content: '\e67c';
}

.icon-role:before {
  content: '\e61b';
}

.icon-user-add:before {
  content: '\e613';
}

.icon-password:before {
  content: '\e734';
}

.icon-exit:before {
  content: '\e61a';
}

.icon-remove:before {
  content: '\e601';
}

.icon-edit:before {
  content: '\e600';
}

.icon-monitor:before {
  content: '\e820';
}

.icon-arrow-right-circle:before {
  content: '\e665';
}

.icon-logout:before {
  content: '\e64b';
}

.icon-reset:before {
  content: '\e63b';
}

.icon-document:before {
  content: '\e619';
}

.icon-message:before {
  content: '\e609';
}

.icon-riseup:before {
  content: '\e617';
}

.icon-strategy:before {
  content: '\e618';
}

.icon-file-open:before {
  content: '\e670';
}

.icon-loading:before {
  content: '\e891';
}

.icon-upload:before {
  content: '\e643';
}

.icon-add-circle:before {
  content: '\e60b';
}

.icon-add:before {
  content: '\e767';
}

.icon-warning-circle-fill:before {
  content: '\e62c';
}

.icon-add-new:before {
  content: '\e627';
}

.icon-account:before {
  content: '\e614';
}

.icon-asset:before {
  content: '\e615';
}

.icon-instruction:before {
  content: '\e616';
}

.icon-list:before {
  content: '\e602';
}

.icon-download:before {
  content: '\e603';
}

.icon-calendar:before {
  content: '\e604';
}

.icon-more:before {
  content: '\e605';
}

.icon-bell:before {
  content: '\e606';
}

.icon-user:before {
  content: '\e607';
}

.icon-setting:before {
  content: '\e608';
}

.icon-home:before {
  content: '\e60a';
}

.icon-category:before {
  content: '\e60c';
}

.icon-exchange:before {
  content: '\e60d';
}

.icon-building:before {
  content: '\e60e';
}

.icon-consist:before {
  content: '\e60f';
}

.icon-plugin:before {
  content: '\e610';
}

.icon-position:before {
  content: '\e611';
}

.icon-block:before {
  content: '\e612';
}
