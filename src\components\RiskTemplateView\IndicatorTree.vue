<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { TreeInstance } from 'element-plus';
import { ElTree } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { Repos, type RiskIndicator } from '../../../../xtrade-sdk/dist';

interface LeafNode {
  id: number;
  code: string;
  name: string;
  indicator: RiskIndicator;
}

interface Level2Node {
  // 第二层节点，依然为分类，id仅为统计控件节点标识
  id: string;
  name: string;
  code: string;
  children: LeafNode[];
}

interface Level1Node {
  // 第一层节点，为分类，id仅为统计控件节点标识
  id: string;
  name: string;
  code: string;
  children: Level2Node[];
}

const repoInstance = new Repos.RiskControlRepo();
// 根据设计图生成树形数据
const treeNodes = ref<Level1Node[]>();

function buildTree(indicators: RiskIndicator[]): Level1Node[] {
  const level_1st_map: { [levelCode: string]: Level1Node } = {};
  indicators.forEach(idc => {
    // check level 1st
    let level1 = level_1st_map[idc.firstLevelCode];
    if (level1 === undefined) {
      level1 = {
        id: idc.firstLevelCode,
        name: idc.firstLevelName,
        code: idc.firstLevelCode,
        children: [],
      };
      level_1st_map[idc.firstLevelCode] = level1;
    }

    // check level 2nd
    let level2 = level1.children.find(n => n.code === idc.secondLevelCode);
    if (level2 === undefined) {
      level2 = {
        id: idc.secondLevelCode,
        name: idc.secondLevelName,
        code: idc.secondLevelCode,
        children: [],
      };
      level1.children.push(level2);
    }

    // add indicator node
    level2.children.push({
      id: idc.id,
      name: idc.indicatorName,
      code: idc.secondLevelCode,
      indicator: idc,
    });
  });

  return Object.values(level_1st_map);
}

async function request() {
  const indicators = (await repoInstance.QueryIndicators()).data || [];
  treeNodes.value = buildTree(indicators);
}

const emitter = defineEmits<{
  select: [item: RiskIndicator];
}>();

const filterText = ref('');
const treeRef = ref<TreeInstance>();

watch(
  () => filterText.value,
  value => {
    treeRef.value!.filter(value);
  },
);

const filterNode = (value: string, data: Level1Node | Level2Node | LeafNode) => {
  if (!value) return true;
  const { name } = data as Level1Node | Level2Node;
  const { indicatorName, firstLevelName, secondLevelName } = (data as LeafNode).indicator;
  const scopes = [name, indicatorName, firstLevelName, secondLevelName];
  const values = scopes.filter(x => typeof x === 'string');
  return values.some(val => val.includes(value));
};

function handleClick(item: LeafNode) {
  emitter('select', item.indicator);
}

function getSelcteds() {
  return (treeRef.value!.getCheckedNodes(true, false) as LeafNode[]).map(x => x.indicator);
}

function getCurrent() {
  const node = treeRef.value!.getCurrentNode() as LeafNode;
  return node?.indicator || null;
}

const indicatorsCountMap = ref<Record<number, number>>({});

function setIndicatorsCount(statistics: { indicatorId: number; count: number }[]) {
  const countMap: Record<number, number> = {};
  statistics.forEach(stat => {
    countMap[stat.indicatorId] = stat.count;
  });
  indicatorsCountMap.value = countMap;
}

onMounted(() => {
  request();
});

defineExpose({
  getCurrent,
  getSelcteds,
  setIndicatorsCount,
});
</script>

<template>
  <div class="tree-control" p-10>
    <!-- 搜索框 -->
    <el-input v-model.trim="filterText" placeholder="搜索指标" :suffix-icon="Search" clearable />
    <!-- 树形结构 -->
    <el-tree
      ref="treeRef"
      empty-text="无指标数据"
      node-key="id"
      :props="{ label: 'name', children: 'children' }"
      :data="treeNodes"
      :filter-node-method="filterNode as any"
      :show-checkbox="false"
      @node-click="handleClick"
      highlight-current
      default-expand-all
    >
      <template #default="{ data }">
        <span>
          <span>{{ data.name }}</span>
          <template v-if="indicatorsCountMap[data.id] !== undefined">
            <span>({{ indicatorsCountMap[data.id] }})</span>
          </template>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<style scoped>
.tree-control {
  :deep() {
    .el-tree-node__label {
      font-size: 14px;
      font-weight: 400;
      color: var(--g-text-color-2);
    }

    .el-tree-node__content:hover {
      background-color: var(--g-block-bg-6);
    }

    .el-tree-node {
      &.is-current {
        > .el-tree-node__content {
          background-color: var(--g-block-bg-6) !important;
        }
      }
    }
  }
}
</style>
