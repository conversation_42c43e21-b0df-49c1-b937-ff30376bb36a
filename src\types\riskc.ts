/**
 * 指标触发项
 */
export interface IndicatorTrigger {
  /**
   * 比较符
   */
  comparer: number;

  /**
   * 阈值
   */
  threshold: number;

  /**
   * 阈值单位
   */
  threholdUnit: string;

  /**
   * 触发项采取动作：1预警 2阻止
   */
  action: number;
}

/**
 * 资产作用域设置
 */
export interface AssetScopeSetting {
  /**
   * 资产类别代码
   */
  kindCodes: string[];

  /**
   * 叠加条件
   */
  overlaies: string[];
}

/**
 * 叠加条件
 */
export interface OverlayCondition {
  variable: string;
  description: string;
  expression: number;
  value: number;
}
