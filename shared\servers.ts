import type { ServerConfig } from '@/types';

const Servers: { [key: string]: ServerConfig } = {
  NewTest: {
    name: 'new-test',
    servers: {
      tradeServer: { host: '***************', port: 8902 },
      quoteServer: { host: '***************', port: 8992 }, // supposed to be
      restServer: { host: '***************', port: 8901 },
    },
  },
  Host08Test: {
    name: 'host08-test',
    servers: {
      tradeServer: { host: '***************', port: 8992 },
      quoteServer: { host: '***************', port: 8992 }, // supposed to be
      restServer: { host: '***************', port: 8980 },
    },
  },
  DevTest: {
    name: 'dev-test',
    servers: {
      tradeServer: { host: '***************', port: 8993 },
      quoteServer: { host: '***************', port: 8992 }, // supposed to be
      restServer: { host: '***************', port: 8994 },
    },
  },
  YLocal: {
    name: 'yl-localhost',
    servers: {
      tradeServer: { host: '***************', port: 8992 },
      quoteServer: { host: '***************', port: 8281 }, // supposed to be
      restServer: { host: '***************', port: 8080 },
    },
  },
  XJHLocal: {
    name: 'xjh-localhost',
    servers: {
      tradeServer: { host: '***************', port: 8992 },
      quoteServer: { host: '***************', port: 8992 }, // supposed to be
      restServer: { host: '***************', port: 8995 },
    },
  },
};

export function getConfigedServers() {
  return Object.values(Servers);
}

export function getMacAddress() {
  return 'D4-5D-64-36-E4-F2,22-49-4D-01-2D-73,04-33-C2-C4-E1-73,74-DF-BF-28-18-3D,70-B5-E8-72-2A-20';
}

export function getOsInfo() {
  const ip = '127.0.0.1';
  const disk_sn = 'DISK:83JD5421D';
  const pc_name = 'DESKTOP-X823J';
  const cpu_sn = 'CPU:6WSH26VC923';
  const disk_partition = 'C:';
  const disk_vol = 'D:';
  const os = [ip, disk_sn, pc_name, cpu_sn, disk_partition, disk_vol];
  return os.join('|');
}
